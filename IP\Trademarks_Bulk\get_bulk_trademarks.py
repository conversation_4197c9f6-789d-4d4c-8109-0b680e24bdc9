#!/usr/bin/env python3
"""
Daily Trademark Report

This script handles the downloading, processing, and database loading of USPTO trademark data.
It supports three modes of operation:
1. Historical Batch Import: One-time bulk import of historical trademark data (pre-2025)
2. Daily Catch-up: Download and process historical daily trademark update files from Jan 1st, 2025
3. Ongoing Daily Processing: Automatically download and process the latest daily trademark update file
"""

import os
import asyncio
import aiohttp
from tqdm.asyncio import tqdm

import argparse
import datetime
import tempfile
from logdata import log_message 
from dotenv import load_dotenv

# Import local modules
# Import the new orchestrator function
from trademark_parser import parse_daily_trademark_xml_parallel
from trademark_db import upsert_trademarks
from trademark_image import download_and_save_image, get_image_subdirectory 
from trademark_file import download_file, extract_zip, send_to_nas, send_folder_to_nas, merge_image_directories
from Common.Constants import local_ip_folder, nas_ip_folder
from embedding_service import EmbeddingQueue

# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗

# Load environment variables
load_dotenv()

# Removed logging configuration block
# logger = logging.getLogger(__name__) # Removed logger initialization

# Constants
# PRE2025_BASE_URL for browser: "https://data.uspto.gov/ui/datasets/products/files/TRTYRAP/apc18840407-20241231-{:02d}.zip" 
# or the list is available at https://data.uspto.gov/bulkdata/datasets/trtyrap?fileDataFromDate=2025-02-24&fileDataToDate=2025-03-24
PRE2025_BASE_URL = "https://api.uspto.gov/api/v1/datasets/products/files/TRTYRAP/apc18840407-20241231-{:02d}.zip"
DAILY_BASE_URL = "https://api.uspto.gov/api/v1/datasets/products/files/TRTDXFAP/apc{}.zip"


# Directory structure
BASE_DIR = os.path.join(local_ip_folder, "Trademarks")
ZIP_DIR = os.path.join(BASE_DIR, "USPTO_Daily", "Zip")
os.makedirs(ZIP_DIR, exist_ok=True)
IMAGES_DIR = os.path.join(BASE_DIR, "USPTO_Daily", "Images")
os.makedirs(IMAGES_DIR, exist_ok=True)

# NAS paths
NAS_ZIP_DIR = f"{nas_ip_folder}/Trademarks/USPTO_Daily/Zip"
NAS_IMAGES_DIR = f"{nas_ip_folder}/Trademarks/USPTO_Daily/Images"

# Maximum concurrent tasks
MAX_CONCURRENT_IMAGES = 10      # For downloading images in parallel

# Removed process_trademark_batch function

# --- XML Parsing logic moved to trademark_parser.py ---
async def process_zip_file(zip_path, extract_dir, nas_dir, source, embedding_queue):
    """
    Process a single ZIP file: extract, parse XML, update database, and send to NAS.

    Args:
        zip_path (str): Path to the ZIP file
        extract_dir (str): Directory to extract to
        nas_dir (str): NAS directory to send the ZIP file to
        embedding_queue (EmbeddingQueue): The queue for image embeddings.

    Returns:
        int: Number of trademark records processed
    """
    try:
        # Start NAS transfer task in parallel
        zip_filename = os.path.basename(zip_path)
        nas_path = f"{nas_dir}/{zip_filename}"
        nas_transfer_task = asyncio.create_task(asyncio.to_thread(send_to_nas, zip_path, nas_path))

        # Extract ZIP file - there should be only one XML file per ZIP
        xml_file = extract_zip(zip_path, extract_dir)

        if not xml_file:
            log_message(f"No XML files found in ZIP: {zip_path}", level='ERROR')
            await nas_transfer_task  # Wait for NAS transfer to complete
            return 0

        try:
            # Call the parallel parsing orchestrator function from trademark_parser
            # This function handles counting, chunking, pool execution, and aggregation
            # Note: This call itself is blocking as pool.starmap is blocking.
            # If process_zip_file needs to yield during parsing, wrap this call
            # in asyncio.to_thread.
            trademarks, serials_to_download = parse_daily_trademark_xml_parallel(xml_file, source=source)

            # If no trademarks were found (either file empty or parsing failed)
            if not trademarks:
                 log_message(f"No trademark records extracted from XML file: {xml_file}", level='WARNING')
                 await nas_transfer_task # Wait for NAS transfer to complete
                 # The finally block below will handle XML deletion
                 return 0 # Return 0 processed records

            # Create a temporary directory for this XML's images (will be deleted automatically)
            with tempfile.TemporaryDirectory(prefix="tm_images_") as temp_images_dir:
                log_message(f"Using temporary image directory: {temp_images_dir}", level='INFO')

                # --- Asynchronous Image Downloading ---
                if serials_to_download:
                    log_message(f"Attempting to download {len(serials_to_download)} images concurrently...", level='INFO')
                    download_semaphore = asyncio.Semaphore(MAX_CONCURRENT_IMAGES)
                    async with aiohttp.ClientSession() as session:
                        # Create progress bar first
                        progress_bar = tqdm(total=len(serials_to_download), desc="Downloading Images")
                        # Update download tasks to include progress bar
                        download_tasks = [
                            download_and_save_image(session, sn, temp_images_dir, IMAGES_DIR, download_semaphore, progress_bar)
                            for sn in serials_to_download
                        ]
                        # Run downloads and capture results
                        results = await asyncio.gather(*download_tasks, return_exceptions=True)

                    # Create a status map from results and collect download statistics
                    download_status = {}
                    download_stats = {"already_present": 0, "newly_downloaded": 0, "failed": 0}

                    for sn, res in zip(serials_to_download, results):
                        if isinstance(res, Exception):
                            log_message(f"Image download failed for {sn} with exception: {res}", level='ERROR')
                            download_status[sn] = False # Treat exception as failure
                            download_stats["failed"] += 1
                        elif isinstance(res, tuple) and len(res) == 2:
                            success, status = res
                            download_status[sn] = success
                            download_stats[status] += 1
                        elif isinstance(res, bool):
                            # Handle legacy boolean return (shouldn't happen with updated function)
                            download_status[sn] = res
                            download_stats["newly_downloaded" if res else "failed"] += 1
                        else:
                            log_message(f"Unexpected result type for {sn} download: {type(res)}", level='ERROR')
                            download_status[sn] = False # Treat unexpected as failure
                            download_stats["failed"] += 1

                    # Print download statistics
                    total_attempted = len(serials_to_download)
                    print(f"📊 Download Statistics for this ZIP file:")
                    print(f"   📁 Already present on HDD: {download_stats['already_present']}")
                    print(f"   ⬇️  Newly downloaded: {download_stats['newly_downloaded']}")
                    print(f"   ❌ Failed downloads: {download_stats['failed']}")
                    print(f"   📈 Total attempted: {total_attempted}")

                    success_count = sum(1 for status in download_status.values() if status)
                    log_message(f"Finished image download attempts. Success count: {success_count}", level='INFO')

                    # Update trademark records with download status
                    for trademark in trademarks:
                        sn = trademark.get('ser_no')
                        mark_feature_code = trademark.get('mark_feature_code')
                        # Only update if download was relevant and attempted
                        if mark_feature_code in [2, 3, 5] and sn in download_status:
                            trademark['image_source'] = "USPTO_URL" if download_status[sn] else "NotFound"
                        # Ensure image_source is set even if download wasn't needed/attempted
                        elif 'image_source' not in trademark:
                            trademark['image_source'] = None

                else:
                    log_message("No images identified for download in this XML.", level='INFO')
                    # Ensure image_source is set to None if not already present
                    for trademark in trademarks:
                        if 'image_source' not in trademark:
                            trademark['image_source'] = None
                # --- End Asynchronous Image Downloading ---

                # The 'trademarks' list now contains updated 'image_source' fields
                total_processed = 0 # Reset total_processed, will be updated by db task

                # Update database in a separate thread (I/O bound) with the final trademark list
                db_task = asyncio.create_task(asyncio.to_thread(upsert_trademarks, trademarks))

                # Send ONLY the temporary image folder to NAS in parallel
                # Check if the temp directory actually contains anything before sending
                if any(os.scandir(temp_images_dir)):
                    log_message(f"Sending images from temporary directory {temp_images_dir} to NAS.", level='INFO')
                    nas_images_task = asyncio.create_task(asyncio.to_thread(send_folder_to_nas, temp_images_dir, NAS_IMAGES_DIR))
                else:
                    log_message("No new images downloaded for this XML, skipping NAS image transfer.", level='INFO')
                    nas_images_task = None # No task to await later

                # Wait for database update to complete
                processed = await db_task
                total_processed += processed

                log_message(f"Processed {processed} trademark records from {xml_file}", level='INFO')

                # Wait for NAS transfers to complete
                await nas_transfer_task
                if nas_images_task:
                    await nas_images_task
                    log_message("NAS image transfer task completed.", level='INFO')

                    # Move images from temp dir to main images dir using the refactored function
                    if not merge_image_directories(temp_images_dir, IMAGES_DIR):
                        # Log error, but don't stop the whole process if moving fails
                        log_message(f"Failed to merge images from {temp_images_dir} to {IMAGES_DIR}. Check trademark_file.log for details.", level='ERROR')
                else:
                    log_message("Skipped moving images as no NAS transfer was initiated.", level='INFO')

                # Enqueue images for embedding after they are in their final location
                images_enqueued = 0
                images_skipped_existing = 0
                for trademark in trademarks:
                    ser_no = trademark.get('ser_no')
                    mark_feature_code = trademark.get('mark_feature_code')

                    # Only enqueue if image was successfully downloaded: Check download_status first (if download was attempted), otherwise check image_source
                    image_was_downloaded = False
                    if mark_feature_code in [2, 3, 5] and ser_no in download_status:
                        # Image download was attempted - use download_status result
                        image_was_downloaded = download_status[ser_no]
                    elif trademark.get('image_source') == "USPTO_URL":
                        # Image was marked as successfully downloaded in database record
                        image_was_downloaded = True

                    if image_was_downloaded:
                        image_sub_dir = get_image_subdirectory(ser_no)
                        if not image_sub_dir:
                            log_message(f"Could not determine image subdirectory for serial number: {ser_no}", level='ERROR')
                            continue # Skip this image if path cannot be determined

                        image_path = os.path.join(IMAGES_DIR, image_sub_dir, f"{ser_no}.webp")
                        enqueued = await embedding_queue.enqueue(ser_no, image_path)
                        if enqueued:
                            images_enqueued += 1
                        else:
                            images_skipped_existing += 1

                log_message(f"Enqueued {images_enqueued} images for embedding processing, skipped {images_skipped_existing} existing embeddings", level='INFO')

                # Temporary directory is automatically cleaned up by the 'with' statement exiting

            return total_processed

        except Exception as e:
            log_message(f"Error processing XML file {xml_file}: {str(e)}", level='ERROR')
            await nas_transfer_task  # Wait for NAS transfer to complete
            return 0

        finally:
            # Delete XML file after processing, with retry logic. Does not work, it says:used by another process.
            xml_files = [file for file in os.listdir(extract_dir) if file.endswith('.xml')]
            for xml_file in xml_files:  
                try:
                    os.remove(os.path.join(extract_dir, xml_file))
                    log_message(f"Deleted XML file: {xml_file}", level='INFO')
                except PermissionError as e:
                    log_message(f"Failed to delete {xml_file}", level='ERROR')

    except Exception as e:
        log_message(f"Error processing ZIP file {zip_path}: {str(e)}", level='ERROR')
        return 0

async def historical_batch_import(embedding_queue):
    """
    Perform a one-time bulk import of historical trademark data (pre-2025).
    There are 155000 per zip file (except maybe the last one).
    There are very few logos in the early files.
    """
    print("Starting historical batch import...")

    # Create directories if they don't exist
    os.makedirs(ZIP_DIR, exist_ok=True)

    # Setup API headers
    api_key = os.getenv("USPTO_ODP_API_KEY")
    headers = {"X-API-KEY": api_key} if api_key else None

    # Download and process each historical ZIP file
    total_processed = 0

    for i in range(5, 6):  # 82 historical ZIP files => range(1, 83)
        zip_url = PRE2025_BASE_URL.format(i)
        zip_filename = f"apc18840407-20241231-{i:02d}.zip"
        zip_path = os.path.join(ZIP_DIR, zip_filename)

        print(f"\n\n{datetime.datetime.now().strftime('%m-%d %H-%M-%S')}: Processing historical file {i}/82: {zip_filename}")

        # Download ZIP file if it doesn't exist
        if not os.path.exists(zip_path):
            print(f"✅ Downloading {zip_filename} from {zip_url}")
            if not download_file(zip_url, zip_path, headers):
                print(f"❌ Failed to download {zip_filename}")
                continue

        # Process ZIP file
        processed = await process_zip_file(zip_path, ZIP_DIR, NAS_ZIP_DIR, source=f'bulk_{zip_filename}', embedding_queue=embedding_queue)
        total_processed += processed

        print(f"✅✅ Processed {processed} trademark records from {zip_filename}")

    print(f"\n\n\n ✅✅Historical batch import completed. Total records processed: {total_processed}\n\n\n")

async def process_date(date, headers=None, embedding_queue=None):
    """
    Process a single date: download, extract, parse, update database, and send to NAS.

    Args:
        date (datetime.date): Date to process
        headers (dict, optional): HTTP headers for API requests
        embedding_queue (EmbeddingQueue, optional): The queue for image embeddings.

    Returns:
        int: Number of trademark records processed
    """
    # Create directories if they don't exist
    os.makedirs(ZIP_DIR, exist_ok=True)

    # Construct file information - format as YYMMDD for daily files
    date_str = date.strftime("%y%m%d")  # Note: lowercase 'y' for 2-digit year
    zip_url = DAILY_BASE_URL.format(date_str)
    zip_filename = f"apc{date_str}.zip"
    zip_path = os.path.join(ZIP_DIR, zip_filename)

    log_message(f"Processing daily file for {date}: {zip_filename}", level='INFO')

    # Download ZIP file if it doesn't exist
    if not os.path.exists(zip_path):
        log_message(f"Downloading {zip_filename} from {zip_url}", level='INFO')
        if not download_file(zip_url, zip_path, headers):
            log_message(f"No file available for {date}", level='WARNING')
            return 0

    # Process ZIP file
    processed = await process_zip_file(zip_path, ZIP_DIR, NAS_ZIP_DIR, source=f'daily_{zip_filename}', embedding_queue=embedding_queue)

    log_message(f"Processed {processed} trademark records from {zip_filename}", level='INFO')
    return processed

async def daily_catchup(start_date, end_date=None, embedding_queue=None):
    """
    Download and process historical daily trademark update files from a specified start date.

    Args:
        start_date (datetime.date): Start date for catchup
        end_date (datetime.date, optional): End date for catchup. Defaults to today.
        embedding_queue (EmbeddingQueue, optional): The queue for image embeddings.
    """
    if end_date is None:
        end_date = datetime.date.today()

    log_message(f"Starting daily catchup from {start_date} to {end_date}...", level='INFO')

    # Setup API headers
    api_key = os.getenv("USPTO_ODP_API_KEY")
    headers = {"X-API-KEY": api_key} if api_key else None

    # Iterate through each date
    current_date = start_date
    total_processed = 0

    while current_date <= end_date:
        processed = await process_date(current_date, headers, embedding_queue)
        total_processed += processed

        # Move to next date
        current_date += datetime.timedelta(days=1)

    log_message(f"Daily catchup completed. Total records processed: {total_processed}", level='INFO')
    return total_processed

async def ongoing_daily_processing(date=None, embedding_queue=None):
    """
    Automatically download and process the latest daily trademark update file.

    Args:
        date (datetime.date, optional): Date to process. Defaults to yesterday.
        embedding_queue (EmbeddingQueue, optional): The queue for image embeddings.
    """
    if date is None:
        # Default to yesterday
        date = datetime.date.today() - datetime.timedelta(days=1)

    log_message(f"Starting ongoing daily processing for {date}...", level='INFO')

    # Setup API headers
    api_key = os.getenv("USPTO_API_KEY")
    headers = {"X-API-KEY": api_key} if api_key else None

    # Process the date
    processed = await process_date(date, headers, embedding_queue)

    log_message(f"Ongoing daily processing completed. Records processed: {processed}", level='INFO')
    return processed

async def test_processing(embedding_queue=None):
    """
    Test function to process a specific date (2025-03-15).
    """
    test_date = datetime.date(2025, 3, 15)
    log_message(f"Running test processing for {test_date}...", level='INFO')

    # Setup API headers
    # api_key = os.getenv("USPTO_BULK_API_KEY")
    api_key = os.getenv("USPTO_ODP_API_KEY")
    headers = {"X-API-KEY": api_key} if api_key else None

    # Process the test date directly
    processed = await process_date(test_date, headers, embedding_queue)

    log_message(f"Test processing completed. Records processed: {processed}", level='INFO')
    return processed

async def test_embedding_queue():
    """
    Test the embedding queue functionality with some dummy data.
    """
    log_message("Starting embedding queue test...", level='INFO')

    # Create embedding queue
    embedding_queue = EmbeddingQueue(max_concurrent=2, num_workers=3)

    try:
        await embedding_queue.start()
        log_message(f"Test embedding queue started: {embedding_queue.get_queue_status()}", level='INFO')

        # Add some test items (using existing image files if they exist)
        test_items = []
        for i in range(5):
            # Create dummy serial numbers
            ser_no = f"test{i:06d}"
            image_path = f"/dummy/path/{ser_no}.jpg"  # This won't exist, but will test the queue
            test_items.append((ser_no, image_path))

        # Enqueue test items
        for ser_no, image_path in test_items:
            await embedding_queue.enqueue(ser_no, image_path)

        log_message(f"Enqueued {len(test_items)} test items: {embedding_queue.get_queue_status()}", level='INFO')

        # Wait a bit to see workers processing
        await asyncio.sleep(10)

        log_message(f"After 10 seconds: {embedding_queue.get_queue_status()}", level='INFO')

        # Drain the queue
        await embedding_queue.drain()
        log_message("Test embedding queue drained successfully", level='INFO')

    finally:
        await embedding_queue.stop()
        log_message("Test embedding queue stopped", level='INFO')

async def test_timeout_urls():
    """
    Test downloading specific URLs that previously timed out.
    """
    urls_to_test = [
        "https://tsdr.uspto.gov/img/79409676/large",
        "https://tsdr.uspto.gov/img/86345847/large",
        "https://tsdr.uspto.gov/img/79358169/large",
        "https://tsdr.uspto.gov/img/75676599/large",
        "https://tsdr.uspto.gov/img/98087874/large",
        "https://tsdr.uspto.gov/img/98726235/large",
        "https://tsdr.uspto.gov/img/79408098/large",
        "https://tsdr.uspto.gov/img/86043382/large",
        "https://tsdr.uspto.gov/img/86256465/large",
        "https://tsdr.uspto.gov/img/79410828/large",
        "https://tsdr.uspto.gov/img/79368020/large",
        "https://tsdr.uspto.gov/img/98536148/large",
    ]
    log_message(f"Starting test download for {len(urls_to_test)} specific URLs...", level='INFO')

    # Extract serial numbers from URLs for the download function
    serials_to_test = [url.split('/')[-2] for url in urls_to_test]

    # Create a temporary directory for test downloads (optional, could download directly)
    with tempfile.TemporaryDirectory(prefix="tm_test_timeout_") as temp_images_dir:
        log_message(f"Using temporary image directory for testing: {temp_images_dir}", level='INFO')
        download_semaphore = asyncio.Semaphore(MAX_CONCURRENT_IMAGES) # Use existing constant
        async with aiohttp.ClientSession() as session:
            download_tasks = [
                # Using the existing download function, saving to temp dir
                # We pass IMAGES_DIR as the final destination, though it might not be strictly needed for just testing
                download_and_save_image(session, sn, temp_images_dir, IMAGES_DIR, download_semaphore)
                for sn in serials_to_test
            ]
            results = await asyncio.gather(*download_tasks, return_exceptions=True)

        success_count = 0
        for sn, res, url in zip(serials_to_test, results, urls_to_test):
            if isinstance(res, Exception):
                log_message(f"Test download FAILED for {sn} ({url}) with exception: {res}", level='ERROR')
            elif isinstance(res, tuple) and len(res) == 2:
                success, status = res
                if success:
                    log_message(f"Test download SUCCEEDED for {sn} ({url}) - {status}", level='INFO')
                    success_count += 1
                else:
                    log_message(f"Test download FAILED for {sn} ({url}) - {status}", level='WARNING')
            elif isinstance(res, bool) and res:
                # Handle legacy boolean return (shouldn't happen with updated function)
                log_message(f"Test download SUCCEEDED for {sn} ({url})", level='INFO')
                success_count += 1
            elif isinstance(res, bool) and not res:
                 log_message(f"Test download FAILED for {sn} ({url}) (returned False)", level='WARNING')
            else:
                log_message(f"Test download for {sn} ({url}) returned unexpected result: {res}", level='ERROR')

        log_message(f"Finished test downloads. Success count: {success_count}/{len(urls_to_test)}", level='INFO')
        # Optionally, clean up the temp directory if needed, but 'with' handles it.
        # If images were successfully downloaded, they might be in temp_images_dir
        # and potentially moved to IMAGES_DIR depending on download_and_save_image logic.

async def async_main(mode, start_date=None, end_date=None, date=None):
    """
    Main function to run the appropriate mode.
    """
    # Create embedding queue with 10 concurrent workers and API rate limiting
    embedding_queue = EmbeddingQueue(max_concurrent=3, num_workers=10)

    try:
        await embedding_queue.start()
        log_message(f"Embedding queue started with status: {embedding_queue.get_queue_status()}", level='INFO')

        if mode == "historical":
            await historical_batch_import(embedding_queue)

        elif mode == "catchup":
            start_date = start_date if start_date else datetime.date(2025, 1, 1)  # Default to Jan 1st, 2025
            end_date = end_date if end_date else datetime.date.today()
            await daily_catchup(start_date, end_date, embedding_queue)

        elif mode == "daily":
            await ongoing_daily_processing(date, embedding_queue)

        elif mode == "test":
            await test_processing(embedding_queue)

        elif mode == "test_embedding":
            await test_embedding_queue()
            # Test has its own embedding queue management
            return

        elif mode == "test_timeouts":
            await test_timeout_urls()
            # No embedding processing needed for test_timeouts
            return

        # Wait for all embedding tasks to complete
        log_message(f"Waiting for embedding queue to drain. Status: {embedding_queue.get_queue_status()}", level='INFO')
        await embedding_queue.drain()
        log_message("All embedding tasks completed", level='INFO')

    finally:
        # Always stop the workers gracefully
        await embedding_queue.stop()

if __name__ == "__main__":
    # Example usage:
    # To run historical import:
    # asyncio.run(main(mode="historical"))
    # To run daily catchup from a specific date:
    # asyncio.run(main(mode="catchup", start_date=datetime.date(2025, 3, 1)))
    # To run daily processing for a specific date:
    # asyncio.run(main(mode="daily", date=datetime.date(2025, 3, 15)))
    # To run test processing:
    # asyncio.run(main(mode="test"))
    # To run embedding queue test:
    # asyncio.run(main(mode="test_embedding"))
    # To run test timeouts:
    # asyncio.run(main(mode="test_timeouts"))
    
    # Default to historical import if no arguments are provided for direct execution
    asyncio.run(async_main(mode="catchup")) 