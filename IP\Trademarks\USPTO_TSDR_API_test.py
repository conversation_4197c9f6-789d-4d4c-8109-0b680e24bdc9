import os,re,time,asyncio,datetime,random,aiohttp,multiprocessing
start_time = time.time()
from bs4 import BeautifulSoup
if multiprocessing.current_process().name == 'MainProcess':
    print(f"                TSDR API BeautifulSoup after  {time.time()-start_time:.2f} seconds")

import numpy as np
from fuzzywuzzy import fuzz
from logdata import log_message # Added logdata import
import json

from IP.Trademarks.USPTO_TSDR_API import TSDRApi, format_reg_number

async def test_api_calls():
    api_client = TSDRApi()
    await api_client.start_session()

    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    base_folder = f"test_results_{timestamp}"
    os.makedirs(base_folder, exist_ok=True)

    test_reg_numbers = ["6,181,442", "1871376", "5004543"]
    test_reg_numbers = [1346436,5546145,6731985,6731984,2854465,7426341,5276717,6731983,6069963,1100741,1075059,5697522,2772224,1314299,1312269,1390935,2276097,7139772,6294707,5428677]

    # Problem reg numbers from case 61411
    # test_reg_numbers =  ['626,035', '902,190', '1,177,400', '1,241,264', '1,241,265', '1,314,511', '1,347,677', '1,501,898', '1,733,051', '1,734,822', '2,559,772', '2,964,843', '3,025,936', '3,133,139', '3,134,695', '3,149,203', '3890159', '4074269', '4241822', '5100448', '5166441', '5280486', '5912273'] # from case 61411
    # test_reg_numbers = ["3306673", ]
    formatted_reg_ids = [format_reg_number(reg_no) for reg_no in test_reg_numbers]
    id_key = 'rn'
    
    # test_ser_numbers = ["85056387", "78758300", "78895949"] # Using dummy serial numbers, replace with real ones if needed
    # formatted_reg_ids = [await api_client.format_ser_number(ser_no, case_date=None, plaintiff_names=None) for ser_no in test_ser_numbers]
    # id_key = 'sn'
    
    # Test serial number that are too short
    # test_ser_numbers = ["65730"] # Using dummy serial numbers, replace with real ones if needed
    # formatted_reg_ids = [await api_client.format_ser_number(ser_no, case_date=datetime.date(2025, 5, 5), plaintiff_names=["Universal City Studios"]) for ser_no in test_ser_numbers]
    # id_key = 'sn'


    async def save_content(content, filename, method_folder):
        if content:
            method_path = os.path.join(base_folder, method_folder)
            os.makedirs(method_path, exist_ok=True)
            filepath = os.path.join(method_path, filename)
            try:
                with open(filepath, 'wb') as f:
                    f.write(content)
                log_message(f"✅ Saved {filename} to {filepath}", level='INFO')
                return True
            except Exception as e:
                log_message(f"🔥 Error saving {filename}: {e}", level='ERROR')
                return False
        else:
            log_message(f"⚠️ No content received for {filename}, not saving.", level='WARNING')
            return False



    log_message("🧪 Starting tests for single registration/serial numbers...", level='INFO')
    for reg_no in formatted_reg_ids: # Test with first 2 reg nos
        # sn_no = test_ser_numbers[test_reg_numbers.index(reg_no)] # Corresponding SN for testing
        # method_folder = f"single_reg_{reg_no}_sn_{sn_no}"
        method_folder = f"single_reg_{reg_no}"

        # This XML has way more info that than one in the Content or Download.
        # xml_content_reg = await api_client.get_status_info_xml(reg_no, id_key=id_key)
        # await save_content(xml_content_reg, f"status_info_reg_{reg_no}.xml", method_folder)

        # Download Zip and Content Zip = markImage.jpg + xml + html. But "Content" seems to have more info than "Download".
        # Download PDF and Content PDF = 1 pdf document that is the same as the xml / html in the zip. "Content" is 10 pages with associated marks, while "Download" is 3 page.
        # download_reg = await api_client.get_status_download(reg_no, id_key=id_key, format='zip')
        # await save_content(download_reg, f"status_download_reg_{reg_no}.zip", method_folder)
        # content_reg = await api_client.get_status_content(reg_no, id_key=id_key, format='zip')
        # await save_content(content_reg, f"status_content_reg_{reg_no}.zip", method_folder)


        # Get a single registration certificate as PDF
        # reg_cert_pdf_content = await api_client.get_casedocs_bundle([reg_no], id_key=id_key, format='zip')
        # await save_content(reg_cert_pdf_content, f"reg_cert_{reg_no}.zip", method_folder)
        # reg_cert_pdf_content = await api_client.get_casedocs_bundle([reg_no], id_key=id_key, format='pdf', option='category=RC')
        # await save_content(reg_cert_pdf_content, f"reg_cert_{reg_no}.pdf", method_folder)


    log_message("🧪 Starting tests for multiple registration numbers...", level='INFO')
    multi_method_folder = "multi_cases"



    # Multi JSON: does not have text, image or certificate => useless
    # multi_json_status = await api_client.get_status_info_json_multi(formatted_reg_ids[:2], id_type='reg')
    # await save_content(multi_json_status, f"multi_reg1.json", multi_method_folder)

    # Bundle
    # This return all the documents into a bundle for multiple cases (or a single case), either PDF or ZIP
    # reg_cert_pdf_content = await api_client.get_casedocs_bundle(formatted_reg_ids[:2], id_type='reg', format='pdf')
    # await save_content(reg_cert_pdf_content, f"multi_reg.pdf", multi_method_folder)
    # reg_cert_pdf_content = await api_client.get_casedocs_bundle(formatted_reg_ids[:2], id_type='reg', format='zip')
    # await save_content(reg_cert_pdf_content, f"multi_reg.zip", multi_method_folder)

    # This is great, return all the certification for multiple cases. If in Zip format => the different certificates are in different format: PDF, TIFF, etc.
    print(f"Total number of reg number {len(formatted_reg_ids)}")
    reg_cert_pdf_content = await api_client.get_casedocs_bundle(formatted_reg_ids[:18], id_key=id_key, format='zip', option='category=RC') # Test with first 2 ser nos
    time.sleep(35)
    reg_cert_pdf_content = await api_client.get_casedocs_bundle(formatted_reg_ids[:20], id_key=id_key, format='zip', option='category=RC') # Test with first 2 ser nos
    # time.sleep(35)
    # reg_cert_pdf_content = await api_client.get_casedocs_bundle(formatted_reg_ids[:22], id_key=id_key, format='zip', option='category=RC') # Test with first 2 ser nos
    # time.sleep(35)
    # reg_cert_pdf_content = await api_client.get_casedocs_bundle(formatted_reg_ids[:24], id_key=id_key, format='zip', option='category=RC') # Test with first 2 ser nos
    # time.sleep(35)

    # await save_content(reg_cert_pdf_content, f"multi_reg_reg_cert.pdf", multi_method_folder)
    # reg_cert_pdf_content = await api_client.get_casedocs_bundle(formatted_reg_ids[:2], id_type='reg', format='zip', option='category=RC') # Test with first 2 ser nos
    # await save_content(reg_cert_pdf_content, f"multi_reg_reg_cert.zip", multi_method_folder)

    # This works, but useless because we do not need the specimen (product images)
    # specimen_pdf_content = await api_client.get_casedocs_bundle(formatted_reg_ids[:2], id_type='reg', format='pdf', option='type=SPE') # Test with first 2 ser nos
    # await save_content(specimen_pdf_content, f"multi_reg_specimen.pdf", multi_method_folder)

    # This is multiple cases in 1 xml. The content is the same as the get_casedocs_bundle(format='xml')
    # This has the link to the Registration certificate in section  <DocumentTypeCodeDescriptionText> Registration Certificate </DocumentTypeCodeDescriptionText> => pdf or tiff. If multiple, take the latest.
    # reg_cert_pdf_content = await api_client.get_casedocs_bundle(formatted_reg_ids[:2], id_type='reg', format='xml')
    # await save_content(reg_cert_pdf_content, f"multi_reg2.xml", multi_method_folder)

    await api_client.close_session()
    log_message(f"✅ All tests completed. Results saved in '{base_folder}'", level='INFO')


if __name__ == "__main__":
    asyncio.run(test_api_calls())